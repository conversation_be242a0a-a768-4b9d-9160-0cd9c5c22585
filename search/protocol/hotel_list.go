package protocol

import (
	"hotel/common/pagehelper"
	commonProto "hotel/common/protocol"
	"hotel/common/types"
	"hotel/content/domain"
	geoDomain "hotel/geography/domain"
	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
)

// HotelListReq represents a request to search for hotels with various filtering options
type HotelListReq struct {
	Operator                       *userDomain.User `json:"operator" apidoc:"-"` // Operator contains the user making the request, excluded from API docs
	HotelIds                       types.IDs        `json:"hotelIds"`            // HotelIds contains specific hotel IDs to search for
	RequireRooms                   bool             `json:"requireRooms"`        // RequireRooms indicates if room details are required; affects QPS limits like HotelRates
	supplierDomain.CheckInOut                       // CheckInOut contains check-in and check-out dates
	supplierDomain.GuestRoomOption                  // GuestRoomOption contains guest and room configuration
	supplierDomain.CurrencyOption                   // CurrencyOption contains currency preferences
	HotelRegion                                     // HotelRegion contains geographical search criteria
	HotelListFilter                                 // HotelListFilter contains additional filtering options
	pagehelper.PageReq                              // PageReq contains pagination parameters
	commonProto.Header                              // Header contains common request metadata
}

// HotelRegion contains geographical search criteria for hotel searches
type HotelRegion struct {
	// RegionId is the region identifier; required if RegionName is not set
	RegionId types.ID `json:"regionId"`

	// RegionName is the region name; required if RegionId is not set; system will auto-search cities with this name
	RegionName string `json:"regionName"`
}

// HotelListFilter contains various filtering options for hotel searches
type HotelListFilter struct {
	Distance          *supplierDomain.DistanceFilter `json:"distance,omitempty"`                             // Distance contains distance-based filtering criteria
	Price             *supplierDomain.PriceFilter    `json:"price,omitempty"`                                // Price contains price range filtering criteria
	InternalSuppliers []supplierDomain.Supplier      `json:"internalSuppliers,omitempty" apidoc:"HotelCode"` // InternalSuppliers contains supplier filters for internal platform

	// RoomTypeIds contains room type IDs to filter by; get possible values from Hotel Content API room types operation
	RoomTypeIds string `json:"roomTypeIds,omitempty"`

	// Additional search filters for enhanced hotel filtering
	HotelName string   `json:"hotelName,omitempty"` // HotelName filters hotels by name
	Rating    *int     `json:"rating,omitempty"`    // Rating filters by minimum rating (1-5)
	SortBy    string   `json:"sortBy,omitempty"`    // SortBy specifies sort order: price-asc, price-desc, rating-desc, distance-asc
	MinPrice  *float64 `json:"minPrice,omitempty"`  // MinPrice sets minimum price threshold
	MaxPrice  *float64 `json:"maxPrice,omitempty"`  // MaxPrice sets maximum price threshold
	Amenities []string `json:"amenities,omitempty"` // Amenities filters by required amenities
}

// HotelListResp represents the response from a hotel list search
type HotelListResp struct {
	List                 domain.HotelList       `json:"list,omitzero"`      // List contains the hotel search results
	Basic                HotelListBasicInfo     `json:"basic,omitzero"`     // Basic contains basic information about the search
	Metadata             map[string]interface{} `json:"metadata,omitempty"` // Metadata contains tracking metadata //apidoc:zh
	*pagehelper.PageResp                        // PageResp contains pagination information
}

// HotelListBasicInfo contains basic information about the hotel search
type HotelListBasicInfo struct {
	Region geoDomain.Region `json:"region"` // Region contains information about the search region
}
