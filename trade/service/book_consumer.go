package service

import (
	"context"
	"encoding/json"
	"fmt"
	"hotel/common/cqrs"
	"strings"
	"time"

	supplierDomain "hotel/supplier/domain"
	"hotel/trade/dao"
	"hotel/trade/domain"

	"hotel/common/log"

	"github.com/bytedance/sonic"
)

const (
	BookTopic        = "book"
	BookTopicChannel = "book_channel"
)

func (s *TradeService) startConsumerBookMessage() error {
	cancelCfg := &cqrs.ConsumerConfig{
		TopicName:    BookTopic,
		ChannelName:  BookTopicChannel,
		ConsumerName: "book_consumer_1",
	}

	// 创建消息处理器，适配新的接口
	handler := func(message []byte) error {
		return s.handleCancelMessage(context.Background(), message)
	}

	consumer, err := cqrs.NewConsumer(s.cqrsConfig, cancelCfg, handler)
	if err != nil {
		log.Error("Failed to create cancel consumer: %+v", err)
		return err
	}

	err = consumer.Start()
	if err != nil {
		log.Error("Failed to start cancel consumer: %v", err)
		return err
	}

	log.Info("Cancel Consumer started successfully. Waiting for messages...")
	return nil
}

//handleBookMessage
/*
这里是预定酒店的重要流程详细逻辑如下
1、首先使用状态机校验当前订单的状态，如果订单状态不是已支付，则直接返回
2、如果订单状态是已支付，则进行预定酒店的逻辑
3、检查当前所有供应商哪些可以提供该房型预定
4、在满足预定条件的供应商中，选择价格最低的供应商进行预定（后续可能会有条件变更，满足规则引擎预定最佳条件）
3、预定酒店的逻辑包括：
	1、调用供应商的预定接口
		* 如果预定成功，则更新订单状态为已确认
		* 如果预定失败，则根据具体失败原因决定是否放弃预定改为待退款
*/
func (s *TradeService) handleBookMessage(ctx context.Context,
	bookMsg []byte) error {
	// 如果已经提交给供应商，需要调用供应商取消接口
	order := &dao.Order{}
	if err := json.Unmarshal(bookMsg, order); err != nil {
		// log.Error("handleBookMsg:(%+v),failed:(%+v)", order, err)
		return err
	}

	// 创建状态机
	sm, err := domain.NewOrderStateMachine(domain.OrderStatus(order.Status))
	if err != nil {
		log.Error("handleBookMsg:(%+v),failed:(%+v)", order, err)
		return err
	}
	// 校验当前订单状态,必须得是domain.OrderStatePaid(已支付)
	if sm.CurrentState() != domain.OrderStatePaid {
		currentStateName, _ := domain.StateName(sm.CurrentState())
		expectedStateName, _ := domain.StateName(domain.OrderStatePaid)
		log.Errorc(context.Background(), "当前订单状态不合法，期望%s状态，实际状态:%s, order:(%+v)",
			expectedStateName, currentStateName, order)
		return fmt.Errorf("invalid order state: expected %s, got %s", expectedStateName, currentStateName)
	}

	// 从订单的 BizInfo 中解析 BookReq
	var bizInfo = new(domain.OrderBizInfo)
	if err := sonic.UnmarshalString(order.BizInfo, bizInfo); err != nil {
		log.Errorc(ctx, "handleBookMsg:(%+v),failed:(%+v)", order, err)
		return err
	}

	checkAvailResp := bizInfo.CheckAvailResp
	sellerPayload := bizInfo.SellerInputPayloads
	bookReq := sellerPayload.Book
	session, err := s.sessionService.GetSession(ctx, bookReq.SessionId)
	if err != nil {
		log.Errorc(ctx, "handleBookMsg:(%+v),failed:(%+v)", order, err)
		return err
	}
	ctx = supplierDomain.InjectBaseRequestContextPayload(ctx, supplierDomain.BaseRequestContextPayload{
		SupplierEntryStartTime: time.Now(),
		Session:                session,
		Header:                 bookReq.Header,
		Properties:             sellerPayload.Properties,
		Supplier:               checkAvailResp.Supplier,
	})
	submitOrderResp, err := s.supplier.Book(ctx, bookReq)
	if err != nil {
		log.Errorc(ctx, "handleBookMsg:(%+v),failed:(%+v)", order, err)
		return err
	}
	log.Warnc(ctx, "handleBookMsg:(%+v),submitOrderResp:(%+v)", order, submitOrderResp)

	// 根据供应商响应更新订单状态
	var newState = domain.ConvertSupplierOrderStatusToTradeStatus(submitOrderResp.OrderStatus)
	var reason = newState.String()
	// 使用状态机进行状态转换
	if err := sm.TransitionToWithReason(newState, reason); err != nil {
		log.Errorc(context.Background(), "Failed to transition order state: %v", err)
		// 状态转换失败，但不影响供应商订单创建
	} else {
		// 更新订单状态到数据库
		order.Status = newState.Int64()
		// 将DAO Order转换为domain Order进行更新
		domainOrder := dao.ConvertDAOOrderToDomain(order)
		if err := s.orderDao.Update(context.Background(), &domainOrder); err != nil {
			log.Errorc(context.Background(), "handleBookMsg:(%+v),Update failed:(%+v)", order, err)
			return err
		}
		log.Infoc(context.Background(), "Order %d transitioned to state %d due to: %s", order.Id, newState, reason)
	}
	// 记录供应商订单数据
	supplierOrder := &dao.SupplierOrder{
		OrderId:         int64(order.Id),
		SupplierId:      1, // TODO: 从checkAvailResp获取实际供应商ID
		SupplierOrderId: submitOrderResp.SupplierOrderId,
		Status:          int64(submitOrderResp.OrderStatus),
		BizInfo:         "{}",
		CreateTime:      time.Now(),
		UpdateTime:      time.Now(),
	}

	// 将DAO SupplierOrder转换为domain SupplierOrder进行插入
	domainSupplierOrder := dao.ConvertDAOSupplierOrderToDomain(supplierOrder)
	err = s.orderDao.InsertSupplierOrder(context.Background(), &domainSupplierOrder)
	// 如果创建供应商订单失败，根据具体失败原因决定是否放弃预定改为待退款
	if err != nil {
		log.Errorc(context.Background(), "handleBookMsg:(%+v),Create supplier_order failed:(%+v)", order, err)

		// 根据错误类型决定订单状态转换
		if strings.Contains(err.Error(), "room not available") ||
			strings.Contains(err.Error(), "price changed") {
			// 房间不可用或价格变化，转为待退款
			reason := fmt.Sprintf("supplier order creation failed: %v", err)
			if transitionErr := sm.TransitionToWithReason(domain.OrderStateNeedRefund, reason); transitionErr != nil {
				log.Errorc(context.Background(), "Failed to transition to NeedRefund: %v", transitionErr)
			} else {
				// 更新数据库状态
				order.Status = domain.OrderStateNeedRefund.Int64()
				domainOrder := dao.ConvertDAOOrderToDomain(order)
				if updateErr := s.orderDao.Update(context.Background(), &domainOrder); updateErr != nil {
					log.Errorc(context.Background(), "Failed to update order status to NeedRefund: %v", updateErr)
				} else {
					log.Infoc(context.Background(), "Order %d transitioned to NeedRefund due to: %v", order.Id, err)
				}
			}
		} else {
			// 其他错误，可能是临时性问题，保持当前状态等待重试
			log.Warnc(context.Background(), "Order %d booking failed with retryable error: %v", order.Id, err)
		}
		return err
	}
	return nil
}
