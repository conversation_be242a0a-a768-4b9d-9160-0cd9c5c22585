package protocol

import (
	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
)

// BookReq represents the request structure for hotel booking operation with 4 fields
type BookReq struct {
	supplierDomain.BookReq                        // BookReq contains the supplier booking request data
	ReferenceNo            string                 `json:"referenceNo,omitempty"` // ReferenceNo contains the reference number value
	Operator               *userDomain.User       `json:"operator" apidoc:"-"`   // Operator contains the user data for this booking
	Metadata               map[string]interface{} `json:"metadata,omitempty"`    // Metadata contains the tracking metadata data //apidoc:zh
}

// BookResp represents the response structure for hotel booking operation with 2 fields
type BookResp struct {
	supplierDomain.BookResp       // BookResp contains the supplier booking response data
	PlatformOrderId         int64 `json:"platformOrderId" required:"true"` // PlatformOrderId represents the platform order identifier as an integer
}
