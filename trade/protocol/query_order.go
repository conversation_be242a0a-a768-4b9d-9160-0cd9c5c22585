package protocol

import (
	"hotel/common/bff"
	"hotel/common/pagehelper"
	"hotel/common/types"
	searchDomain "hotel/content/domain"
	geoDomain "hotel/geography/domain"
	"hotel/trade/domain"
	userDomain "hotel/user/domain"
)

// QueryOrdersReq represents a request to query multiple orders
type QueryOrdersReq struct {
	QueryOrderCriteria // QueryOrderCriteria contains the search criteria for orders
}

// QueryOrdersResp represents the response containing multiple orders
type QueryOrdersResp struct {
	Orders []domain.Order `json:"orders"` // Orders contains the list of matching orders
}

// QueryOrderCriteria contains various criteria for querying orders
type QueryOrderCriteria struct {
	PlatformOrderIds   types.IDs            `json:"platformOrderIds"`   // PlatformOrderIds contains specific platform order IDs to query
	ReferenceNos       []string             `json:"referenceNos"`       // ReferenceNos contains reference numbers to search for
	CheckInTimeWindow  types.TimeWindow     `json:"checkInTimeWindow"`  // CheckInTimeWindow filters orders by check-in date range
	CheckOutTimeWindow types.TimeWindow     `json:"checkOutTimeWindow"` // CheckOutTimeWindow filters orders by check-out date range
	CreateTimeWindow   types.TimeWindow     `json:"createTimeWindow"`   // CreateTimeWindow filters orders by creation date range
	CancelTimeWindow   types.TimeWindow     `json:"cancelTimeWindow"`   // CancelTimeWindow filters orders by cancellation date range
	StatusList         []domain.OrderStatus `json:"statusList"`         // StatusList filters orders by status
	Tags               []string             `json:"tags"`               // Tags filters orders by associated tags
}

// ListOrderReq represents a request to list orders with pagination
type ListOrderReq struct {
	QueryOrderCriteria        // QueryOrderCriteria contains the search criteria
	InternalKeyword    string `json:"keyword"` // InternalKeyword is an internal search keyword
	pagehelper.PageReq        // PageReq contains pagination parameters
}

// ListOrderResp represents the response for order listing with pagination
type ListOrderResp struct {
	bff.Table[domain.Order] // Table contains paginated order data with generic Order type
}

// DetailOrderReq represents a request to get detailed order information
type DetailOrderReq struct {
	OrderId types.ID `json:"orderId"` // OrderId is the unique identifier for the order
}

// DetailOrderResp represents the detailed order information response
type DetailOrderResp struct {
	Summary   *domain.OrderSummary   `json:"summary"`   // Summary contains order summary information
	Account   *DetailOrderAccount    `json:"account"`   // Account contains account-related information
	SubOrders []*DetailOrderSubOrder `json:"subOrders"` // SubOrders contains detailed sub-order information
}

// DetailOrderSubOrder contains detailed information about a sub-order
type DetailOrderSubOrder struct {
	Hotel   *DetailOrderHotel                  `json:"hotel"`   // Hotel contains hotel-specific information for the sub-order
	Booking bff.Table[domain.OrderSummary]     `json:"booking"` // Booking contains booking summary information
	Gain    bff.Table[domain.OrderGainSummary] `json:"gain"`    // Gain contains gain summary information
}

// DetailOrderHotel contains hotel information for order details
type DetailOrderHotel struct {
	*searchDomain.HotelSummary        // HotelSummary contains basic hotel information
	ConfirmNumber              string `json:"confirmNumber"` // ConfirmNumber is the hotel confirmation number //apidoc:zh
	OrderId                    string `json:"orderId"`       // OrderId is the platform order number //apidoc:zh
}

// DetailOrderAccount contains account information for order details
type DetailOrderAccount struct {
	*domain.OrderAccount                              // OrderAccount contains basic account information
	CustomerBuyer        bff.Table[userDomain.Entity] `json:"customerBuyer,omitzero"` // CustomerBuyer contains customer buyer information
}

// OrderHomeFunctionReq represents a request for order home function data
type OrderHomeFunctionReq struct {
}

// OrderHomeFunctionResp represents the response for order home function data
type OrderHomeFunctionResp struct {
	Tags                 []string                  `json:"tags"`                 // Tags contains available order tags
	CountryOrderCounters []geoDomain.RegionCounter `json:"countryOrderCounters"` // CountryOrderCounters contains order counts by country
	RecommendedOrders    bff.Table[domain.Order]   `json:"recommendedOrders"`    // RecommendedOrders contains recommended orders for default display //apidoc:zh
}
