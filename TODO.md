# Hotel-BE 项目 TODO 任务清单

## 工具链（tool）

- [ ] [P0] **文件**: `build/api/docgen.go:120`；实现 upload 函数 - 实现文档上传功能，支持将本地文档上传到远程服务器
- [ ] [P2] **文件**: `build/api/asthelper/error_code.go:19,36`；错误码优化 - 处理一个 httpcode 可能有多个的情况、考虑对外对内错误码分开，根据
  code 大小判断
- [ ] [P2] **文件**: `build/api/asthelper/parser.go:110`；自定义扩展 - 实现自定义扩展功能
- [ ] [P2] **文件**: `build/api/asthelper/response_builder.go:25`；响应构建优化 - 优化响应构建逻辑
- [ ] [P2] **文件**: `build/api/asthelper/comment.go:198`；插件自定义 - 实现插件自定义功能
- [ ] [P2] **文件**: `build/api/asthelper/type_parser.go:98`；泛型参数支持 - 扩展支持多个泛型参数，目前仅支持单个
- [ ] [P2] **文件**: `build/api/docgen.go:341`；文档生成优化 - 优化文档生成逻辑
- [ ] [P2] **文件**: `common/pdf/pdf.go:80`；中文字体支持 - 添加中文字体支持

## 中间件

- [ ] [P1] **文件**: `common/cqrs/cqrs.go:82,125`；实现 NSQ 适配器 - 实现 NSQ Producer 和 Consumer 适配器，支持 NSQ 消息队列

## 基础设施

- [ ] [P0] 将服务器 500，panic 等系统异常接入 sentry告警

## 交易（trade）

- [ ] [P0] **文件**: `trade/service/rebooking_api.go:56`；实现重预订统计功能 - 查询数据库，统计重预订的各种指标（成功率、节省金额等）
- [ ] [P0] **文件**: `trade/service/rebooking_service.go:194`；实现配置存储和应用逻辑 -
  实现重预订配置的存储和应用，包括启用/禁用特定供应商、时间窗口设置等
- [ ] [P0] **文件**: `trade/service/cancel_audit.go:160`；完善审计日志系统 -
  在实际项目中实现：写入专门的审计日志表、发送到审计日志系统（如ELK）、发送到监控系统（如Prometheus）、发送到告警系统
- [ ] [P1] **文件**: `trade/service/order_scanner.go:40`；重构以适配新的 CQRS 接口 - 重构订单扫描器，使其适配新的 CQRS
  消息发布接口
- [ ] [P2] **文件**: `trade/service/book_consumer.go:136`；从 checkAvailResp 获取实际供应商ID - 修改硬编码的供应商ID，从
  checkAvailResp 中获取实际的供应商ID
- [ ] [P1] **文件**: `trade/service/book.go:97`；数据库保存重试机制 - 实现数据库保存的重试机制
- [ ] [P1] **文件**: `trade/service/cancel_consumer.go:173`；计算实际处理时间 - 实现实际处理时间的计算，而不是使用硬编码值

## BFF（api）

- [ ] [P3] **文件**: `api/service/view.go:51`；添加日志记录 - 在 starlingSrv 加载失败时添加详细的日志记录，便于问题排查

## 搜索（search）

- [ ] [P2] **文件**: `search/service/hotel_price_cache.go:246,261,282`；实现价格获取逻辑 - 实现通过 HotelList 获取价格的逻辑、批量
  HotelRates 查询逻辑、限流的单个 HotelRates 查询
- [ ] [P2] **文件**: `search/service/hotel_rates.go:244,259`；动态修改房间能力和房间映射集成 - 实现动态修改 rooms 的能力、集成

## 酒店房型映射（mapping）

- [ ] [P1] **文件**: `mapping/supplier/giata/service.go:81`；GIATA酒店ID列表获取逻辑 - 根据实际API实现GIATA酒店ID列表获取逻辑

## 内容（content）

- [ ] [P1] **文件**: `content/mysql/hotel_dao.go:281`；统一搜索币种转换 - 将价格转换为统一的搜索币种
- [ ] [P1] **文件**: `content/service/database.go:115`；优化酒店选择逻辑 - 改进酒店选择逻辑，避免只取第一个酒店

## 供应商（supplier）

- [ ] [P1] **文件**: `supplier/ctrip/book.go:13,32`；实现请求构建 - 实现 CheckAvail 的请求构建、实现 Cancel 的请求构建
- [ ] [P1] **文件**: `supplier/netstorming/serviceutil.go:28`；儿童年龄对齐 - 实现儿童年龄对齐逻辑，确保不同供应商的儿童年龄标准一致
- [ ] [P1] **文件**: `supplier/dida/model/check_avail.go:80`；解析价格变化和取消政策变化 - 实现价格变化和取消政策变化的解析逻辑
- [ ] [P1] **文件**: `supplier/dida/service.go:78`；国家代码搜酒店ID列表 - 实现通过国家代码搜索酒店ID列表的功能
- [ ] [P1] **文件**: `supplier/factory.go:49`；自动化发现注册 - 实现自动化发现注册功能，只能依赖数据库
- [ ] [P1] **文件**: `supplier/derbysoft/full_flow_test.go:60`；补充完整流程测试 - 后续可补充 Book、QueryOrder、Cancel
    等完整流程的测试

## 用户（user）

- [ ] [P1] **文件**: `user/service/invite.go:107`；实现重新发送邀请功能 - 通过邀请的审计日志找到前一条邀请，续期，然后重新发送邀请邮件
- [ ] [P1] **文件**: `user/service/invite.go:241,244`；精细的域名管理 - 实现精细的域名管理功能，支持不同用户类型的域名配置
- [ ] [P2] **文件**: `user/domain/user.go:20,65,248,403`；用户角色和权限优化 - 改进角色管理机制、优化当前确认逻辑，支持扩展、优化
  scope 结构，支持 path 单分支结构、评估名称是否合适
- [ ] [P2] **文件**: `user/domain/entity.go:54,60`；数组越界处理和缓存优化 - 处理数组越界问题、为 GetScope 方法添加缓存
- [ ] [P2] **文件**: `user/mysql/entity_dao.go:78`；添加缓存 - 为实体DAO添加缓存机制
- [ ] [P2] **文件**: `user/mysql/supplier_credential_dao.go:18`；添加缓存 - 为供应商凭证DAO添加缓存机制
- [ ] [P2] **文件**: `user/protocol/invite.go:36,39,46`；登录态和角色优化 - 优化登录态固定字段、完善下面的逻辑、直接使用
  role，userRole 应该用在别处
- [ ] [P2] **文件**: `user/protocol/user.go:12`；默认值处理 - 优化默认值处理逻辑
- [ ] [P2] **文件**: `user/service/user.go:60`；检查请求实体ID - 实现 req.EntityIDs 的检查逻辑

## 地理（geography）

- [ ] [P2] **文件**: `geography/domain/ctrip_hotel_city.go:250,251`；完善国家代码信息 - 完善 CountryCode 和
  CountrySubdivisionCode 字段
- [ ] [P2] **文件**: `geography/service/init.go:41`；初始化优化 - 优化地理模块的初始化逻辑

## 商业智能（BI）

- [ ] [P2] **文件**: `bi/domain/ctx.go:48`；工具方法补充 - 补充 utils 方法，用于方便地往 context 里插入 hbLog 的字段

## 前端

- [ ] [P2] **文件**: `admin-fe/src/views/system/logs/components/LogDetailDialog.vue:305`；实现单个日志导出 - 实现单个日志的导出功能
- [ ] [P2] **文件**: `admin-fe/src/views/booking/hotel-search/index.vue:585`；实现画廊视图 - 实现酒店搜索结果的画廊视图展示

# 使用说明

- [ ] 表示任务未完成
- [x] 表示任务已完成
- 可以通过勾选复选框来跟踪任务进度
- 完成后请勾选已完成

## 建议

1. **优先处理高优先级任务**，特别是影响核心功能的 TODO
2. **按模块分组处理**，避免在多个模块间频繁切换
3. **建立任务跟踪机制**，定期更新任务状态
4. **代码审查时关注 TODO**，避免新增 TODO 而不处理
5. **考虑自动化测试**，确保 TODO 完成后功能正常
