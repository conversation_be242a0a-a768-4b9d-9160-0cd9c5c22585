package domain

import (
	"hotel/common/pagehelper"
	"hotel/common/types"
	geoDomain "hotel/geography/domain"
)

// HotelListReq represents a request to retrieve a list of hotels from suppliers
type HotelListReq struct {
	SupplierHotelIds   []string `json:"supplierHotelIds"` // SupplierHotelIds contains specific supplier hotel IDs to search for (optional)
	CheckInOut                  // CheckInOut contains check-in and check-out dates
	GuestRoomOption             // GuestRoomOption contains guest and room configuration
	HotelRegion                 // HotelRegion contains geographical search criteria
	HotelListFilter             // HotelListFilter contains filtering options
	Ranking                     // Ranking contains sorting preferences
	pagehelper.PageReq          // PageReq contains pagination parameters
}

// CheckInOut contains check-in and check-out date information
type CheckInOut struct {
	CheckIn  types.DateInt `json:"checkIn" required:"true" default:"now()"`    // CheckIn is the check-in date, defaults to current date
	CheckOut types.DateInt `json:"checkOut" required:"true" default:"now()+7"` // CheckOut is the check-out date, defaults to 7 days from now
}

// CountryOption contains country-related information for booking context
type CountryOption struct {
	// CountryCode is the country code of the booker's point of sale in ISO 3166-1 alpha-2 format (e.g., "US")
	// This represents the country where the transaction is taking place
	CountryCode string `json:"countryCode"`
	// ResidencyCode is the residency code of the booker in ISO 3166-1 alpha-2 format (e.g., "US")
	ResidencyCode string `json:"residencyCode"`
	// NationalityCode is the nationality code of the booker in ISO 3166-1 alpha-2 format (e.g., "US")
	NationalityCode string `json:"nationalityCode"`
}

// GetNationalityCode returns the most appropriate nationality code based on available information
func (c CountryOption) GetNationalityCode() string {
	if c.NationalityCode != "" {
		return c.NationalityCode
	}
	if c.ResidencyCode != "" {
		return c.ResidencyCode
	}
	return c.CountryCode
}

// GuestRoomOption contains guest and room configuration for hotel searches
type GuestRoomOption struct {
	CountryOption         // CountryOption contains country-related information
	RoomCount     int64   `json:"roomCount"`     // RoomCount is the number of rooms required, defaults to 1
	AdultCount    int64   `json:"adultCount"`    // AdultCount is the number of adults per room, defaults to 1
	ChildrenCount int64   `json:"childrenCount"` // ChildrenCount is the number of children per room
	Guests        []Guest `json:"guests"`        // Guests contains detailed guest information
}

func (o GuestRoomOption) GetRoomCount() int64 {
	if o.RoomCount <= 0 {
		o.RoomCount = 1
	}
	return o.RoomCount
}

type HotelRegion struct {
	RegionName     string `json:"regionName"`     // optional
	SupplierCityId string `json:"supplierCityId"` // optional
}

// Ranking contains sorting preferences for hotel search results
type Ranking struct {
	Order   SearchSortType // Order specifies the sort direction (ascending/descending)
	OrderBy SearchSortName // OrderBy specifies the field to sort by
}

// HotelListFilter contains filtering criteria for hotel searches
type HotelListFilter struct {
	Distance     *DistanceFilter `json:"distance"`     // Distance contains distance-based filtering criteria
	Price        *PriceFilter    `json:"price"`        // Price contains price range filtering criteria
	FreeCancel   *bool           `json:"freeCancel"`   // FreeCancel indicates if free cancellation is required
	HasBreakfast *bool           `json:"hasBreakfast"` // HasBreakfast indicates if breakfast inclusion is required
}

// HotelListResp represents the response from a hotel list request
type HotelListResp struct {
	Hotels              []SupplierHotel `json:"hotels"` // Hotels contains the list of hotels from suppliers
	pagehelper.PageResp                 // PageResp contains pagination information
}

// DistanceFilter contains distance-based filtering criteria
type DistanceFilter struct {
	geoDomain.Nearby // Nearby contains geographical proximity information
}

// PriceFilter contains price range filtering criteria
type PriceFilter struct {
	LowPrice  float64 `json:"lowPrice"`  // LowPrice is the minimum price threshold
	HighPrice float64 `json:"highPrice"` // HighPrice is the maximum price threshold
}

// KeywordTypeEnum represents different types of search keywords
type KeywordTypeEnum int64

const (
	HOTEL           KeywordTypeEnum = 1  // HOTEL represents hotel name searches
	HOTEL_BRAND     KeywordTypeEnum = 2  // HOTEL_BRAND represents hotel brand searches
	HOTEL_GROUP     KeywordTypeEnum = 3  // HOTEL_GROUP represents hotel group searches
	CITY            KeywordTypeEnum = 4  // CITY represents city name searches
	SCENIC_AREA     KeywordTypeEnum = 5  // SCENIC_AREA represents scenic area searches
	CORP_PLACE      KeywordTypeEnum = 6  // CORP_PLACE represents corporate place searches
	RAILWAY_STATION KeywordTypeEnum = 7  // RAILWAY_STATION represents railway station searches
	LANDMARK        KeywordTypeEnum = 8  // LANDMARK represents landmark searches
	LOCATION        KeywordTypeEnum = 9  // LOCATION represents general location searches
	AIRPORT         KeywordTypeEnum = 10 // AIRPORT represents airport searches
	INTL_AIRPORT    KeywordTypeEnum = 11 // INTL_AIRPORT represents international airport searches
	METRO_STATION   KeywordTypeEnum = 12 // METRO_STATION represents metro station searches
	ZONE            KeywordTypeEnum = 13 // ZONE represents zone or area searches
	OTHER           KeywordTypeEnum = 14 // OTHER represents other types of searches
)

func (p KeywordTypeEnum) String() string {
	switch p {
	case HOTEL:
		return "HOTEL"
	case HOTEL_BRAND:
		return "HOTEL_BRAND"
	case HOTEL_GROUP:
		return "HOTEL_GROUP"
	case CITY:
		return "CITY"
	case SCENIC_AREA:
		return "SCENIC_AREA"
	case CORP_PLACE:
		return "CORP_PLACE"
	case RAILWAY_STATION:
		return "RAILWAY_STATION"
	case LANDMARK:
		return "LANDMARK"
	case LOCATION:
		return "LOCATION"
	case AIRPORT:
		return "AIRPORT"
	case INTL_AIRPORT:
		return "INTL_AIRPORT"
	case METRO_STATION:
		return "METRO_STATION"
	case ZONE:
		return "ZONE"
	case OTHER:
		return "OTHER"
	default:
		return "<UNSET>"
	}
}
