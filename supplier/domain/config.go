package domain

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
)

// SupplierAPI contains API endpoint configurations for a supplier
type SupplierAPI struct {
	API map[string]APIEndpoint `json:"api"` // API contains a map of API endpoint configurations
}

// APIEndpoint contains configuration for a specific API endpoint
type APIEndpoint struct {
	Path           string            `json:"path"`                       // Path is the API endpoint path
	ContentType    string            `json:"contentType,default=json"`   // ContentType specifies the request content type, defaults to json
	BaseURL        string            `json:"baseURL,omitempty"`          // BaseURL is the base URL for the API endpoint
	HttpMethod     string            `json:"httpMethod,default=POST"`    // HttpMethod specifies the HTTP method, defaults to POST
	TimeoutSeconds int64             `json:"timeoutSeconds,default=30"`  // TimeoutSeconds specifies the request timeout, defaults to 30 seconds
	EnableEncode   bool              `json:"enableEncode,default=false"` // EnableEncode indicates if request encoding is enabled, defaults to false
	CustomHeaders  map[string]string `json:"customHeaders,omitempty"`    // CustomHeaders contains custom request headers //apidoc:zh
}

// Config contains the overall configuration for supplier integrations
type Config struct {
	API   map[string]APIEndpoint `json:"API" yaml:"API"` // API contains API endpoint configurations
	Redis redis.RedisConf        // Redis contains Redis configuration
}
