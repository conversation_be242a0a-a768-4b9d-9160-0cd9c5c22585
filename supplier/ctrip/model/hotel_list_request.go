package model

// HotelListReq represents a request to search for hotels in the Ctrip system
type HotelListReq struct {
	BaseInfo       *BaseInfo       `json:"baseInfo"`        // BaseInfo contains basic request information
	RoomFilter     *RoomFilter     `json:"roomFilterInfo"`  // RoomFilter contains room-specific filtering criteria
	HotelFilter    *HotelFilter    `json:"hotelFilterInfo"` // HotelFilter contains hotel-specific filtering criteria
	SearchBaseInfo *SearchBaseInfo `json:"searchBaseInfo"`  // SearchBaseInfo contains core search parameters
	Prop           Properties      `json:"-"`               // Prop contains internal properties, not serialized
}

// RoomFilter contains filtering criteria for room-related attributes
type RoomFilter struct {
	RoomInfoFilter   *RoomInfoFilter   `json:"roomInfoFilter"`   // RoomInfoFilter contains room information filters
	RoomPolicyFilter *RoomPolicyFilter `json:"roomPolicyFilter"` // RoomPolicyFilter contains room policy filters
	RoomPriceRange   *RoomPriceRange   `json:"roomPriceRange"`   // RoomPriceRange contains price range filters
}

// RoomInfoFilter contains filters for room information attributes
type RoomInfoFilter struct {
	BedType    string `json:"bedType"`    // BedType specifies the preferred bed type
	WindowType string `json:"windowType"` // WindowType specifies the preferred window type
}

// RoomPolicyFilter contains filters for room policies and features
type RoomPolicyFilter struct {
	OnlyPPRoom            *bool                                `json:"guestQuantity"`         // OnlyPPRoom indicates if only per-person rooms are desired
	HasBreakfast          *bool                                `json:"hasBreakfast"`          // HasBreakfast indicates if breakfast inclusion is required
	CompanyAccountPayment *bool                                `json:"companyAccountPayment"` // CompanyAccountPayment indicates if company account payment is required
	FreeCancel            *bool                                `json:"freeCancel"`            // FreeCancel indicates if free cancellation is required
	OnlyBonusPoint        *bool                                `json:"onlyBonusPoint"`        // OnlyBonusPoint indicates if only bonus point eligible rooms are desired
	ApplicativeAreaInfo   *RoomPolicyFilterApplicativeAreaInfo `json:"applicativeAreaInfo"`   // ApplicativeAreaInfo contains area-specific applicability filters
}

// RoomPolicyFilterApplicativeAreaInfo contains area-specific applicability information
type RoomPolicyFilterApplicativeAreaInfo struct {
	ForeignGuestsApplicative bool `json:"foreignGuestsApplicative"` // ForeignGuestsApplicative indicates if applicable to foreign guests
	GatApplicative           bool `json:"gATApplicative"`           // GatApplicative indicates if GAT (Government Affairs Travel) applicable
}

// RoomPriceRange contains price range filtering criteria
type RoomPriceRange struct {
	LowPrice  float64 `json:"lowPrice"`  // LowPrice is the minimum price threshold
	HighPrice float64 `json:"highPrice"` // HighPrice is the maximum price threshold
}

// HotelFilter contains filtering criteria for hotel-related attributes
type HotelFilter struct {
	HotelInfoFilter     *HotelInfoFilter     `json:"hotelInfoFilter"`     // HotelInfoFilter contains hotel information filters
	HotelPositionFilter *HotelPositionFilter `json:"hotelPositionFilter"` // HotelPositionFilter contains location-based filters
}

// HotelInfoFilter contains filters for hotel information and characteristics
type HotelInfoFilter struct {
	HotelBrandGroupFilter  *HotelBrandGroupFilter `json:"hotelBrandGroupInfo"`    // HotelBrandGroupFilter contains brand and group filtering criteria
	HotelStarList          []int32                `json:"hotelStar"`              // HotelStarList contains desired star ratings
	OnlyViewAgreementHotel *bool                  `json:"onlyViewAgreementHotel"` // OnlyViewAgreementHotel indicates if only agreement hotels should be shown
	Keyword                string                 `json:"keyword"`                // Keyword is the search keyword for hotel names
	HotelCommentFilter     *HotelCommentFilter    `json:"hotelCommentInfo"`       // HotelCommentFilter contains review and rating filters
}

// HotelBrandGroupFilter contains filtering criteria for hotel brands and groups
type HotelBrandGroupFilter struct {
	HotelBrandList        []int32  `json:"hotelBrand"`        // HotelBrandList contains desired hotel brand IDs
	HotelGroupList        []int32  `json:"hotelGroup"`        // HotelGroupList contains desired hotel group IDs
	HotelBrandFeatureList []string `json:"hotelBrandFeature"` // HotelBrandFeatureList contains desired brand features
}

// HotelCommentFilter contains filtering criteria for hotel reviews and ratings
type HotelCommentFilter struct {
	MaxComments *int32   `json:"maxComments"` // MaxComments is the maximum number of comments threshold
	MinComments *int32   `json:"minComments"` // MinComments is the minimum number of comments threshold
	MaxRating   *float64 `json:"maxRating"`   // MaxRating is the maximum rating threshold
	MinRating   *float64 `json:"minRating"`   // MinRating is the minimum rating threshold
}

// HotelPositionFilter contains location-based filtering criteria
type HotelPositionFilter struct {
	MapSearchInfo *MapSearchInfo `json:"mapSearchInfo"` // MapSearchInfo contains map-based search parameters
}

// MapSearchInfo contains map-based search parameters
type MapSearchInfo struct {
	Lat     float64 `json:"lat"`     // Lat is the latitude coordinate for the search center
	Lon     float64 `json:"lon"`     // Lon is the longitude coordinate for the search center
	MapType string  `json:"mapType"` // MapType indicates the type of map coordinate system
	Radius  float64 `json:"radius"`  // Radius is the search radius in kilometers
}

// HotelFacilitiesFilter contains filtering criteria for hotel facilities and amenities
type HotelFacilitiesFilter struct {
	HasAirportShuttle     bool `json:"hasAirportShuttle"`     // HasAirportShuttle indicates if airport shuttle service is required
	HasFitnessCenter      bool `json:"hasFitnessCenter"`      // HasFitnessCenter indicates if fitness center is required
	HasSwimmingPool       bool `json:"hasSwimmingPool"`       // HasSwimmingPool indicates if swimming pool is required
	HasParking            bool `json:"hasParking"`            // HasParking indicates if parking facility is required
	HasAirportPickup      bool `json:"hasAirportPickup"`      // HasAirportPickup indicates if airport pickup service is required
	ChineseFriendly       bool `json:"chineseFriendly"`       // ChineseFriendly indicates if Chinese-friendly services are required //apidoc:zh
	Spa                   bool `json:"sPA"`                   // Spa indicates if spa facility is required
	FreeWirelessBroadband bool `json:"freeWirelessBroadband"` // FreeWirelessBroadband indicates if free wireless internet is required
	FreeWiredBroadband    bool `json:"freeWiredBroadband"`    // FreeWiredBroadband indicates if free wired internet is required
}

// SearchBaseInfo contains core search parameters for hotel list requests
type SearchBaseInfo struct {
	HotelIDList   []int       `json:"hotelIdList"`   // HotelIDList contains specific hotel IDs to search for
	CityID        int32       `json:"cityId"`        // CityID is the city identifier for the search
	CheckInDate   string      `json:"checkInDate"`   // CheckInDate is the check-in date in YYYY-MM-DD format
	CheckOutDate  string      `json:"checkOutDate"`  // CheckOutDate is the check-out date in YYYY-MM-DD format
	PagingInfo    *PagingInfo `json:"pagingInfo"`    // PagingInfo contains pagination parameters
	SortInfo      *SortInfo   `json:"sortInfo"`      // SortInfo contains sorting parameters
	GuestQuantity int64       `json:"guestQuantity"` // GuestQuantity is the total number of guests
	RoomQuantity  int64       `json:"roomQuantity"`  // RoomQuantity is the number of rooms required
}

// PagingInfo contains pagination parameters for search results
type PagingInfo struct {
	PageIndex int64 `json:"pageIndex"` // PageIndex is the page number (0-based)
	PageSize  int64 `json:"pageSize"`  // PageSize is the number of results per page
}

// SortInfo contains sorting parameters for search results
type SortInfo struct {
	SortType      string `json:"sortType"`      // SortType specifies the field to sort by
	SortDirection string `json:"sortDirection"` // SortDirection specifies ascending or descending order
}
