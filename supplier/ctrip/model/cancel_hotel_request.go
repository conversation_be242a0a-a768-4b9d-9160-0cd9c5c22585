package model

// CancelHotelReq represents a request to cancel a hotel booking in the Ctrip system
type CancelHotelReq struct {
	CorpID          string            `json:"CorpID"`             // CorpID is the corporate identifier
	OrderID         int64             `json:"OrderID"`            // OrderID is the unique order identifier to cancel
	UID             string            `json:"UID"`                // U<PERSON> is the user identifier
	SID             string            `json:"SID"`                // SID is the session identifier
	EID             string            `json:"EID"`                // EID is the enterprise identifier
	CancelReason    string            `json:"CancelReason"`       // CancelReason contains the reason for cancellation
	ApplyCancelFlag bool              `json:"ApplyCancelFlag"`    // ApplyCancelFlag indicates whether to apply the cancellation
	CancelType      string            `json:"CancelType"`         // CancelType specifies the type of cancellation (full, partial, etc.)
	CancelRoomInfo  []*CancelRoomInfo `json:"CancelRoomInfoList"` // CancelRoomInfo contains details of rooms to cancel
}

// CancelRoomInfo contains information about specific rooms to cancel
type CancelRoomInfo struct {
	Date     string `json:"Date"`     // Date is the date for which the room should be cancelled
	Quantity int    `json:"Quantity"` // Quantity is the number of rooms to cancel for this date
}
